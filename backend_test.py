#!/usr/bin/env python3
"""
Backend API Testing Suite for Solo Level Up
Tests the beta signup and statistics endpoints
"""

import requests
import json
import time
import uuid
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('/app/frontend/.env')

# Get backend URL from environment
BACKEND_URL = os.getenv('REACT_APP_BACKEND_URL', 'http://localhost:8001')
API_BASE_URL = f"{BACKEND_URL}/api"

print(f"Testing backend API at: {API_BASE_URL}")

class BetaSignupTester:
    def __init__(self):
        self.test_emails = []
        self.results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
    
    def log_result(self, test_name, passed, message=""):
        if passed:
            print(f"✅ {test_name}: PASSED")
            self.results['passed'] += 1
        else:
            print(f"❌ {test_name}: FAILED - {message}")
            self.results['failed'] += 1
            self.results['errors'].append(f"{test_name}: {message}")
    
    def test_api_connectivity(self):
        """Test basic API connectivity"""
        try:
            response = requests.get(f"{API_BASE_URL}/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                expected_message = "Solo Level Up System Online"
                if data.get('message') == expected_message:
                    self.log_result("API Connectivity", True)
                    return True
                else:
                    self.log_result("API Connectivity", False, f"Unexpected message: {data}")
                    return False
            else:
                self.log_result("API Connectivity", False, f"Status code: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("API Connectivity", False, f"Connection error: {str(e)}")
            return False
    
    def test_valid_email_signup(self):
        """Test successful email signup with valid email format"""
        test_email = f"warrior{uuid.uuid4().hex[:8]}@levelup.com"
        self.test_emails.append(test_email)
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/beta-signup",
                json={"email": test_email},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check response structure
                required_fields = ['success', 'message', 'data']
                if all(field in data for field in required_fields):
                    if data['success'] and data['data']:
                        # Check data fields
                        data_fields = ['id', 'email', 'timestamp', 'status', 'position']
                        if all(field in data['data'] for field in data_fields):
                            # Verify email is lowercased
                            if data['data']['email'] == test_email.lower():
                                self.log_result("Valid Email Signup", True)
                                return True
                            else:
                                self.log_result("Valid Email Signup", False, "Email not properly lowercased")
                        else:
                            self.log_result("Valid Email Signup", False, "Missing data fields")
                    else:
                        self.log_result("Valid Email Signup", False, "Success false or missing data")
                else:
                    self.log_result("Valid Email Signup", False, "Missing required response fields")
            else:
                self.log_result("Valid Email Signup", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Valid Email Signup", False, f"Request error: {str(e)}")
        
        return False
    
    def test_duplicate_email_prevention(self):
        """Test duplicate email prevention"""
        if not self.test_emails:
            self.log_result("Duplicate Email Prevention", False, "No test emails available")
            return False
        
        duplicate_email = self.test_emails[0]
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/beta-signup",
                json={"email": duplicate_email},
                timeout=10
            )
            
            if response.status_code == 400:
                data = response.json()
                detail = data.get('detail', {})
                if (detail.get('success') == False and 
                    detail.get('error') == 'DESIGNATION_EXISTS'):
                    self.log_result("Duplicate Email Prevention", True)
                    return True
                else:
                    self.log_result("Duplicate Email Prevention", False, f"Unexpected error format: {detail}")
            else:
                self.log_result("Duplicate Email Prevention", False, f"Expected 400, got {response.status_code}")
        except Exception as e:
            self.log_result("Duplicate Email Prevention", False, f"Request error: {str(e)}")
        
        return False
    
    def test_invalid_email_format(self):
        """Test invalid email format validation"""
        invalid_emails = [
            "invalid-email",
            "test@",
            "@domain.com",
            "<EMAIL>",
            "test@domain",
            ""
        ]
        
        passed_count = 0
        for invalid_email in invalid_emails:
            try:
                response = requests.post(
                    f"{API_BASE_URL}/beta-signup",
                    json={"email": invalid_email},
                    timeout=10
                )
                
                # FastAPI with Pydantic EmailStr returns 422 for validation errors
                if response.status_code == 422:
                    data = response.json()
                    # Check if it's a validation error for email field
                    if 'detail' in data and isinstance(data['detail'], list):
                        for error in data['detail']:
                            if (error.get('loc') == ['body', 'email'] and 
                                'email' in error.get('msg', '').lower()):
                                passed_count += 1
                                break
                    else:
                        print(f"  Invalid email '{invalid_email}': Wrong error format")
                elif response.status_code == 400:
                    # Also accept 400 if custom validation catches it
                    data = response.json()
                    detail = data.get('detail', {})
                    if (detail.get('success') == False and 
                        detail.get('error') == 'INVALID_DESIGNATION'):
                        passed_count += 1
                    else:
                        print(f"  Invalid email '{invalid_email}': Wrong error format")
                else:
                    print(f"  Invalid email '{invalid_email}': Expected 422 or 400, got {response.status_code}")
            except Exception as e:
                print(f"  Invalid email '{invalid_email}': Request error: {str(e)}")
        
        if passed_count == len(invalid_emails):
            self.log_result("Invalid Email Format Validation", True)
            return True
        else:
            self.log_result("Invalid Email Format Validation", False, 
                          f"Only {passed_count}/{len(invalid_emails)} invalid emails properly rejected")
            return False
    
    def test_email_case_handling(self):
        """Test email case handling (should be lowercased)"""
        mixed_case_email = f"WARRIOR{uuid.uuid4().hex[:8]}@LEVELUP.COM"
        self.test_emails.append(mixed_case_email.lower())
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/beta-signup",
                json={"email": mixed_case_email},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if (data.get('success') and 
                    data.get('data', {}).get('email') == mixed_case_email.lower()):
                    self.log_result("Email Case Handling", True)
                    return True
                else:
                    self.log_result("Email Case Handling", False, "Email not properly lowercased")
            else:
                self.log_result("Email Case Handling", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Email Case Handling", False, f"Request error: {str(e)}")
        
        return False
    
    def test_position_counter_increment(self):
        """Test position counter increment"""
        # Get current stats
        try:
            stats_response = requests.get(f"{API_BASE_URL}/beta-signup/stats", timeout=10)
            if stats_response.status_code != 200:
                self.log_result("Position Counter Increment", False, "Could not get initial stats")
                return False
            
            initial_count = stats_response.json().get('totalSignups', 0)
            
            # Add a new signup
            test_email = f"position{uuid.uuid4().hex[:8]}@levelup.com"
            signup_response = requests.post(
                f"{API_BASE_URL}/beta-signup",
                json={"email": test_email},
                timeout=10
            )
            
            if signup_response.status_code == 200:
                signup_data = signup_response.json()
                expected_position = initial_count + 1
                actual_position = signup_data.get('data', {}).get('position')
                
                if actual_position == expected_position:
                    self.log_result("Position Counter Increment", True)
                    return True
                else:
                    self.log_result("Position Counter Increment", False, 
                                  f"Expected position {expected_position}, got {actual_position}")
            else:
                self.log_result("Position Counter Increment", False, "Signup failed")
        except Exception as e:
            self.log_result("Position Counter Increment", False, f"Request error: {str(e)}")
        
        return False
    
    def test_beta_stats_retrieval(self):
        """Test statistics retrieval"""
        try:
            response = requests.get(f"{API_BASE_URL}/beta-signup/stats", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ['totalSignups', 'totalSlots', 'percentageFilled']
                
                if all(field in data for field in required_fields):
                    # Verify calculation
                    expected_percentage = (data['totalSignups'] / data['totalSlots']) * 100
                    if abs(data['percentageFilled'] - expected_percentage) < 0.01:
                        self.log_result("Beta Stats Retrieval", True)
                        return True
                    else:
                        self.log_result("Beta Stats Retrieval", False, "Percentage calculation incorrect")
                else:
                    self.log_result("Beta Stats Retrieval", False, "Missing required fields")
            else:
                self.log_result("Beta Stats Retrieval", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Beta Stats Retrieval", False, f"Request error: {str(e)}")
        
        return False
    
    def test_response_format_validation(self):
        """Test API response format matches contract"""
        test_email = f"format{uuid.uuid4().hex[:8]}@levelup.com"
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/beta-signup",
                json={"email": test_email},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check response structure matches BetaSignupResponse model
                if (isinstance(data.get('success'), bool) and
                    isinstance(data.get('message'), str) and
                    isinstance(data.get('data'), dict)):
                    
                    # Check data structure matches expected format
                    data_obj = data['data']
                    if (isinstance(data_obj.get('id'), str) and
                        isinstance(data_obj.get('email'), str) and
                        isinstance(data_obj.get('timestamp'), str) and
                        isinstance(data_obj.get('status'), str) and
                        isinstance(data_obj.get('position'), int)):
                        
                        self.log_result("Response Format Validation", True)
                        return True
                    else:
                        self.log_result("Response Format Validation", False, "Data object format incorrect")
                else:
                    self.log_result("Response Format Validation", False, "Response structure incorrect")
            else:
                self.log_result("Response Format Validation", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Response Format Validation", False, f"Request error: {str(e)}")
        
        return False
    
    def test_system_error_handling(self):
        """Test system error handling with malformed requests"""
        test_cases = [
            {"data": {}, "description": "Empty request body"},
            {"data": {"email": None}, "description": "Null email"},
            {"data": {"invalid_field": "test"}, "description": "Invalid field"},
        ]
        
        passed_count = 0
        for test_case in test_cases:
            try:
                response = requests.post(
                    f"{API_BASE_URL}/beta-signup",
                    json=test_case["data"],
                    timeout=10
                )
                
                # Should return 4xx error for malformed requests
                if 400 <= response.status_code < 500:
                    passed_count += 1
                else:
                    print(f"  {test_case['description']}: Expected 4xx, got {response.status_code}")
            except Exception as e:
                print(f"  {test_case['description']}: Request error: {str(e)}")
        
        if passed_count >= len(test_cases) - 1:  # Allow one failure
            self.log_result("System Error Handling", True)
            return True
        else:
            self.log_result("System Error Handling", False, 
                          f"Only {passed_count}/{len(test_cases)} error cases handled properly")
            return False
    
    def run_all_tests(self):
        """Run all backend tests"""
        print("=" * 60)
        print("SOLO LEVEL UP BACKEND API TESTING")
        print("=" * 60)
        
        # Test API connectivity first
        if not self.test_api_connectivity():
            print("\n❌ API connectivity failed. Stopping tests.")
            return self.results
        
        print("\n🧪 Running Beta Signup API Tests...")
        
        # Core functionality tests
        self.test_valid_email_signup()
        self.test_duplicate_email_prevention()
        self.test_invalid_email_format()
        self.test_email_case_handling()
        self.test_position_counter_increment()
        
        # Stats API tests
        print("\n📊 Running Beta Stats API Tests...")
        self.test_beta_stats_retrieval()
        
        # Response format and error handling tests
        print("\n🔍 Running Response Format and Error Handling Tests...")
        self.test_response_format_validation()
        self.test_system_error_handling()
        
        # Print summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        if self.results['errors']:
            print("\n🚨 FAILED TESTS:")
            for error in self.results['errors']:
                print(f"  • {error}")
        
        success_rate = (self.results['passed'] / (self.results['passed'] + self.results['failed'])) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        return self.results

if __name__ == "__main__":
    tester = BetaSignupTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    exit(0 if results['failed'] == 0 else 1)