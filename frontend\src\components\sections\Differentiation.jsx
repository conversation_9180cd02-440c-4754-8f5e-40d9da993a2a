import React from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';

const Differentiation = () => {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.3 });

  return (
    <section className="section" id="differentiation" ref={sectionRef}>
      <div className="section-content">
        <div className="max-w-6xl mx-auto px-4">
          <h2 
            className={`headline-font text-3xl sm:text-4xl md:text-5xl font-bold mb-12 sm:mb-16 text-center transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
            style={{ color: '#ffffff' }}
          >
            This is not a habit tracker.
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            {/* Left Side - The Old Way */}
            <div 
              className={`text-center transition-all duration-800 delay-300 ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-8'
              }`}
            >
              <h3 
                className="terminal-font text-lg sm:text-xl mb-6"
                style={{ color: '#666666', letterSpacing: '1px' }}
              >
                THE OLD WAY
              </h3>
              
              <div 
                className="bg-gray-800 p-6 sm:p-8 rounded-lg opacity-40 hover:opacity-60 transition-opacity duration-300"
                style={{ 
                  background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
                  border: '1px solid #333333'
                }}
              >
                <div className="space-y-4">
                  <div className="h-3 sm:h-4 bg-green-400 rounded w-3/4 mx-auto opacity-50"></div>
                  <div className="h-3 sm:h-4 bg-blue-400 rounded w-1/2 mx-auto opacity-50"></div>
                  <div className="h-3 sm:h-4 bg-yellow-400 rounded w-2/3 mx-auto opacity-50"></div>
                  <div className="text-gray-400 text-xs sm:text-sm mt-6">
                    ✓ Drink water<br/>
                    ✓ Go for a walk<br/>
                    ○ Read a book<br/>
                    <span className="text-green-400">Great job! 🎉</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - The System */}
            <div 
              className={`text-center transition-all duration-800 delay-500 ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
              }`}
            >
              <h3 
                className="terminal-font text-lg sm:text-xl mb-6"
                style={{ color: '#00BFFF', letterSpacing: '1px' }}
              >
                THE SYSTEM
              </h3>
              
              <div 
                className="relative"
                style={{ filter: 'brightness(1.2) contrast(1.1)' }}
              >
                <img 
                  src="https://images.unsplash.com/photo-1739561439496-f2d1be7503d8"
                  alt="The System Interface"
                  className="w-full h-auto rounded-lg blue-glow"
                  style={{ 
                    border: '1px solid #00BFFF',
                    boxShadow: '0 0 30px rgba(0, 191, 255, 0.4)'
                  }}
                />
                
                {/* Overlay with System elements */}
                <div className="absolute inset-0 flex flex-col justify-center items-center text-center p-2 sm:p-4">
                  <div className="bg-black/80 p-2 sm:p-4 border border-blue-400 rounded max-w-xs">
                    <div className="text-xs space-y-1 sm:space-y-2">
                      <div style={{ color: '#0080FF' }}>HP: 85/100  MP: 42/60</div>
                      <div style={{ color: '#ffffff' }}>STR: 7  INT: 9  DIS: 5  PER: 6</div>
                      <div className="border-t border-gray-600 pt-1 sm:pt-2">
                        <div style={{ color: '#cccccc' }}>[QUEST LOG]</div>
                        <div style={{ color: '#FF003C' }}>[PENALTY QUEST] Complete workout - FAILED</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div 
            className={`text-center mt-8 sm:mt-12 transition-all duration-1000 delay-700 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <p 
              className="terminal-font text-lg sm:text-xl"
              style={{ color: '#ffffff', letterSpacing: '1px' }}
            >
              They gave you suggestions. The System gives you <span style={{ color: '#00BFFF' }}>commands</span>.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Differentiation;