import React, { useState, useEffect } from 'react';

const TypewriterText = ({ 
  text, 
  isVisible, 
  speed = 50, 
  className = '', 
  showCursor = true,
  onComplete = () => {},
  delay = 0 
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [showBlinkingCursor, setShowBlinkingCursor] = useState(true);

  useEffect(() => {
    if (!isVisible || currentIndex >= text.length) return;

    const delayTimer = setTimeout(() => {
      setIsTyping(true);
      
      const timer = setInterval(() => {
        setCurrentIndex(prev => {
          const newIndex = prev + 1;
          
          if (newIndex >= text.length) {
            setIsTyping(false);
            onComplete();
            return prev;
          }
          
          setDisplayText(text.slice(0, newIndex + 1));
          return newIndex;
        });
      }, speed);

      return () => clearInterval(timer);
    }, delay);

    return () => clearTimeout(delayTimer);
  }, [isVisible, text, speed, currentIndex, onComplete, delay]);

  useEffect(() => {
    if (!isTyping && currentIndex >= text.length) {
      const cursorTimer = setInterval(() => {
        setShowBlinkingCursor(prev => !prev);
      }, 500);
      
      return () => clearInterval(cursorTimer);
    }
  }, [isTyping, currentIndex, text.length]);

  return (
    <span className={className}>
      {displayText}
      {showCursor && isTyping && (
        <span style={{ color: '#00BFFF' }}>_</span>
      )}
      {showCursor && !isTyping && currentIndex >= text.length && (
        <span 
          style={{ 
            color: '#00BFFF',
            opacity: showBlinkingCursor ? 1 : 0,
            transition: 'opacity 0.1s'
          }}
        >
          _
        </span>
      )}
    </span>
  );
};

export default TypewriterText;