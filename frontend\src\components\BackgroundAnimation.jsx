import React, { useEffect, useState } from 'react';

const BackgroundAnimation = () => {
  const [embers, setEmbers] = useState([]);

  useEffect(() => {
    const createEmber = () => {
      return {
        id: Math.random(),
        left: Math.random() * 100 + '%',
        animationDuration: (Math.random() * 20 + 10) + 's',
        animationDelay: Math.random() * 5 + 's',
        opacity: Math.random() * 0.5 + 0.2,
      };
    };

    const initialEmbers = Array.from({ length: 15 }, createEmber);
    setEmbers(initialEmbers);

    const interval = setInterval(() => {
      setEmbers(prev => {
        const newEmbers = [...prev];
        const randomIndex = Math.floor(Math.random() * newEmbers.length);
        newEmbers[randomIndex] = createEmber();
        return newEmbers;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {embers.map(ember => (
        <div
          key={ember.id}
          className="digital-ember"
          style={{
            left: ember.left,
            animationDuration: ember.animationDuration,
            animationDelay: ember.animationDelay,
            opacity: ember.opacity,
          }}
        />
      ))}
    </div>
  );
};

export default BackgroundAnimation;