from fastapi import Fast<PERSON><PERSON>, APIRout<PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional
import uuid
from datetime import datetime
import re
from pymongo import ASCENDING
from pymongo.errors import DuplicateKeyError


ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")


# Define Models
class StatusCheck(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    client_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class StatusCheckCreate(BaseModel):
    client_name: str

class BetaSignupRequest(BaseModel):
    email: EmailStr

class BetaSignupResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

class BetaSignup(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: EmailStr
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field(default="pending")
    position: Optional[int] = None

class BetaStatsResponse(BaseModel):
    totalSignups: int
    totalSlots: int = 1000
    percentageFilled: float


# Email validation function
def validate_email(email: str) -> bool:
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


# Initialize database indexes
async def setup_database():
    # Create unique index on email for beta_signups collection
    await db.beta_signups.create_index([("email", ASCENDING)], unique=True)
    logging.info("Database indexes created successfully")


# Add your routes to the router instead of directly to app
@api_router.get("/")
async def root():
    return {"message": "Solo Level Up System Online"}

@api_router.post("/beta-signup", response_model=BetaSignupResponse)
async def create_beta_signup(signup_request: BetaSignupRequest):
    try:
        email = signup_request.email.lower()
        
        # Validate email format
        if not validate_email(email):
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": "INVALID_DESIGNATION",
                    "message": "Email format invalid"
                }
            )
        
        # Get current count for position calculation
        current_count = await db.beta_signups.count_documents({})
        
        # Create beta signup document
        beta_signup = BetaSignup(
            email=email,
            position=current_count + 1
        )
        
        # Insert into database
        result = await db.beta_signups.insert_one(beta_signup.dict())
        
        return BetaSignupResponse(
            success=True,
            message="DESIGNATION LOGGED. AWAIT THE CALL.",
            data={
                "id": beta_signup.id,
                "email": beta_signup.email,
                "timestamp": beta_signup.timestamp.isoformat(),
                "status": beta_signup.status,
                "position": beta_signup.position
            }
        )
        
    except DuplicateKeyError:
        raise HTTPException(
            status_code=400,
            detail={
                "success": False,
                "error": "DESIGNATION_EXISTS",
                "message": "DESIGNATION ALREADY LOGGED"
            }
        )
    except Exception as e:
        logging.error(f"Beta signup error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "SYSTEM_ERROR",
                "message": "SYSTEM TEMPORARILY UNAVAILABLE"
            }
        )

@api_router.get("/beta-signup/stats", response_model=BetaStatsResponse)
async def get_beta_stats():
    try:
        total_signups = await db.beta_signups.count_documents({})
        total_slots = 1000
        percentage_filled = (total_signups / total_slots) * 100
        
        return BetaStatsResponse(
            totalSignups=total_signups,
            totalSlots=total_slots,
            percentageFilled=round(percentage_filled, 2)
        )
    except Exception as e:
        logging.error(f"Beta stats error: {str(e)}")
        raise HTTPException(status_code=500, detail="Unable to retrieve stats")

# Legacy endpoints (keep for compatibility)
@api_router.post("/status", response_model=StatusCheck)
async def create_status_check(input: StatusCheckCreate):
    status_dict = input.dict()
    status_obj = StatusCheck(**status_dict)
    _ = await db.status_checks.insert_one(status_obj.dict())
    return status_obj

@api_router.get("/status", response_model=List[StatusCheck])
async def get_status_checks():
    status_checks = await db.status_checks.find().to_list(1000)
    return [StatusCheck(**status_check) for status_check in status_checks]

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    await setup_database()

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()
