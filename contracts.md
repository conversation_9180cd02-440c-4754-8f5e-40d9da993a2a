# API Contracts - Solo Level Up System

## Overview
This document outlines the API contracts for transitioning from mock localStorage implementation to real backend database integration.

## Current Mock Implementation
- **File**: `/app/frontend/src/data/mock.js`
- **Storage**: localStorage simulation
- **Primary Function**: `mockAPI.submitBetaSignup(email)`

## Backend API Endpoints Required

### 1. Email Signup Endpoint
**Endpoint**: `POST /api/beta-signup`

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "DESIGNATION LOGGED. AWAIT THE CALL.",
  "data": {
    "id": "unique_signup_id",
    "email": "<EMAIL>", 
    "timestamp": "2024-12-07T10:30:00Z",
    "status": "pending",
    "position": 42
  }
}
```

**Response Error (400)**:
```json
{
  "success": false,
  "error": "INVALID_DESIGNATION",
  "message": "Email format invalid or already exists"
}
```

### 2. Signup Status Endpoint (Optional)
**Endpoint**: `GET /api/beta-signup/stats`

**Response**:
```json
{
  "totalSignups": 156,
  "totalSlots": 1000,
  "percentageFilled": 15.6
}
```

## Database Schema

### BetaSignups Collection
```javascript
{
  _id: ObjectId,
  email: String (required, unique, validated),
  timestamp: Date (default: now),
  status: String (enum: ['pending', 'approved', 'rejected'], default: 'pending'),
  ipAddress: String (for analytics),
  userAgent: String (for analytics),
  position: Number (auto-increment for queue position)
}
```

## Frontend Integration Points

### Current Mock Calls to Replace:
1. **InitiationPortal.jsx**: Line ~35 - `mockAPI.submitBetaSignup(email)`
2. **Remove**: All localStorage usage in mock.js

### Integration Steps:
1. Create API utility file: `/app/frontend/src/utils/api.js`
2. Replace mock calls with real API calls using axios
3. Update error handling to match backend responses
4. Maintain same UI flow and state management

## Validation Rules
- **Email**: RFC 5322 compliant, max 254 characters
- **Rate Limiting**: 3 attempts per IP per hour
- **Duplicate Prevention**: Check existing emails before insert
- **Data Sanitization**: Clean all inputs before database storage

## Error Handling
- Network errors: Show "SYSTEM TEMPORARILY UNAVAILABLE"
- Validation errors: Show "INVALID DESIGNATION FORMAT" 
- Duplicate email: Show "DESIGNATION ALREADY LOGGED"
- Rate limit: Show "TOO MANY ATTEMPTS. WAIT FOR COOLDOWN"

## Security Considerations
- CORS properly configured for frontend domain
- Input validation and sanitization
- Rate limiting implementation
- No sensitive data exposure in responses
- Proper error messages without system details

## Testing Requirements
- Unit tests for email validation
- Integration tests for API endpoints
- Error scenario testing
- Database constraint testing
- Frontend-backend integration testing