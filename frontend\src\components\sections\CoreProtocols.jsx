import React from 'react';
import { Target, TrendingUp, Eye, Shield } from 'lucide-react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import audioManager from '../../utils/audioUtils';

const CoreProtocols = ({ audioEnabled }) => {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.3 });

  const protocols = [
    {
      icon: <Target size={32} />,
      title: "REALITY QUESTS",
      description: "Your goals are now quests. Your effort is now XP. Failure has consequences."
    },
    {
      icon: <TrendingUp size={32} />,
      title: "STAT EVOLUTION",
      description: "Forge your build. Increase real-world Strength, Intelligence, and Discipline. Your stats are a reflection of your will."
    },
    {
      icon: <Eye size={32} />,
      title: "THE SYSTEM NARRATOR",
      description: "An omnipresent AI tracks your progress, issues commands, and delivers judgment. You are never alone."
    },
    {
      icon: <Shield size={32} />,
      title: "THE LEVEL 5 THRESHOLD",
      description: "The tutorial is free. Your freedom is not. After Level 5, the System will demand a tribute."
    }
  ];

  return (
    <section className="section" id="core-protocols" ref={sectionRef}>
      <div className="section-content">
        <div className="max-w-6xl mx-auto px-4">
          <h2 
            className={`headline-font text-3xl sm:text-4xl md:text-5xl font-bold mb-12 sm:mb-16 text-center transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
            style={{ color: '#ffffff' }}
          >
            Core Protocols
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            {protocols.map((protocol, index) => (
              <div
                key={index}
                className={`protocol-item border p-6 sm:p-8 bg-black/50 transition-all duration-300 ease-out hover:scale-[1.02] ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{
                  borderColor: '#333333',
                  transition: `all 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.6s ease ${index * 0.2}s, transform 0.6s ease ${index * 0.2}s`,
                  transitionDelay: isVisible ? `${index * 0.2}s` : '0s',
                  willChange: 'transform, border-color, box-shadow'
                }}
                onMouseEnter={(e) => {
                  const element = e.currentTarget;
                  element.style.borderColor = '#00BFFF';
                  element.style.boxShadow = '0 0 30px rgba(0, 191, 255, 0.4), 0 0 60px rgba(0, 191, 255, 0.1)';
                  element.style.transform = 'translateY(-4px) scale(1.02)';
                  console.log(`🎮 Hover: Protocol ${index + 1} - ${protocol.title}`);
                  audioManager.playHover();
                }}
                onMouseLeave={(e) => {
                  const element = e.currentTarget;
                  element.style.borderColor = '#333333';
                  element.style.boxShadow = 'none';
                  element.style.transform = 'translateY(0) scale(1)';
                }}
              >
                <div 
                  className="flex items-center justify-center w-16 h-16 mb-6 mx-auto"
                  style={{ color: '#00BFFF' }}
                >
                  {protocol.icon}
                </div>
                
                <h3 
                  className="terminal-font text-lg sm:text-xl font-bold mb-4 text-center"
                  style={{ color: '#0080FF', letterSpacing: '1px' }}
                >
                  {protocol.title}
                </h3>
                
                <p 
                  className="terminal-font text-sm leading-relaxed text-center"
                  style={{ color: '#cccccc' }}
                >
                  {protocol.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoreProtocols;