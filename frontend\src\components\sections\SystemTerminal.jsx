import React, { useState, useEffect } from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import audioManager from '../../utils/audioUtils';

const SystemTerminal = ({ audioEnabled }) => {
  const [sectionRef, isVisible, hasBeenSeen] = useIntersectionObserver({ threshold: 0.1 });
  const [displayText, setDisplayText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [typingComplete, setTypingComplete] = useState(false);
  
  const terminalText = `> SYSTEM BOOT SEQUENCE COMPLETE.
> SEARCHING FOR CANDIDATE... POTENTIAL DETECTED.
> AWAKENING PROTOCOL INITIATED. YOUR REALITY IS NOW A BETA TEST.
> WELCOME, PLAYER.`;

  useEffect(() => {
    if (hasBeenSeen && !typingComplete) {
      let index = 0;
      setDisplayText('');

      // Start opening sound (looped background music)
      console.log('🎵 Starting opening sound and typing animation');
      audioManager.playOpening();

      const timer = setInterval(() => {
        if (index < terminalText.length) {
          setDisplayText(terminalText.slice(0, index + 1));

          // Play typing sound for each character (but not too frequently)
          if (index % 3 === 0) { // Play every 3rd character to avoid overwhelming
            audioManager.playTyping();
          }

          index++;
        } else {
          setTypingComplete(true);
          // Stop opening sound when typing is complete
          audioManager.stopOpening();
          clearInterval(timer);
        }
      }, 30);

      return () => {
        clearInterval(timer);
        // Cleanup: stop opening sound if component unmounts
        audioManager.stopOpening();
      };
    }
  }, [hasBeenSeen, terminalText, typingComplete]);

  // Always show cursor
  useEffect(() => {
    const cursorTimer = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorTimer);
  }, []);

  return (
    <section className="section" id="system-terminal" ref={sectionRef}>
      <div className="section-content">
        <div className="max-w-4xl mx-auto px-4">
          <div 
            className="border bg-black/95 p-6 sm:p-8 rounded-none shadow-lg"
            style={{ 
              borderColor: '#00BFFF',
              fontFamily: 'Space Mono, monospace',
              boxShadow: '0 0 20px rgba(0, 191, 255, 0.4)'
            }}
          >
            <pre className="text-sm md:text-base leading-relaxed whitespace-pre-wrap">
              <span style={{ color: '#00BFFF' }}>
                {displayText}
              </span>
              <span 
                className={`inline-block ml-1 ${showCursor ? 'opacity-100' : 'opacity-0'}`}
                style={{ 
                  color: '#00BFFF',
                  fontSize: 'inherit'
                }}
              >
                _
              </span>
            </pre>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SystemTerminal;