import { useState, useEffect, useRef } from 'react';

const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasBeenSeen, setHasBeenSeen] = useState(false);
  const targetRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        setIsIntersecting(isVisible);
        
        // Once seen, always mark as seen (for one-time animations)
        if (isVisible && !hasBeenSeen) {
          setHasBeenSeen(true);
        }
      },
      {
        threshold: 0.3,
        rootMargin: '-50px 0px -50px 0px',
        ...options,
      }
    );

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => {
      if (targetRef.current) {
        observer.unobserve(targetRef.current);
      }
    };
  }, [hasBeenSeen]);

  return [targetRef, isIntersecting, hasBeenSeen];
};

export default useIntersectionObserver;