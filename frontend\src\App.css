@import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700;900&family=Space+Mono:ital,wght@0,400;0,700;1,400&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Space Mono', monospace;
  background: #000000;
  color: #ffffff;
  overflow-x: hidden;
  cursor: auto;
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: 12px;
  height: 12px;
  background: #00BFFF;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: screen;
  animation: cursor-glow 2s ease-in-out infinite alternate;
  transition: transform 0.1s ease;
}

@keyframes cursor-glow {
  0% { transform: scale(1); opacity: 0.8; box-shadow: 0 0 10px #00BFFF; }
  100% { transform: scale(1.3); opacity: 1; box-shadow: 0 0 20px #00BFFF, 0 0 30px #00FFFF; }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: #00BFFF;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00FFFF;
}

/* Typography */
.headline-font {
  font-family: 'Cinzel Decorative', serif;
  font-weight: 700;
}

.terminal-font {
  font-family: 'Space Mono', monospace;
  font-weight: 400;
}

/* Color variables */
:root {
  --absolute-black: #000000;
  --neon-blue: #00BFFF;
  --electric-blue: #0080FF;
  --cyan-glow: #00FFFF;
  --blood-red: #FF003C;
  --text-white: #ffffff;
  --text-gray: #cccccc;
  --dark-blue: #001a33;
}

/* Glitch effect */
@keyframes glitch {
  0% { transform: translateX(0); }
  20% { transform: translateX(-2px); }
  40% { transform: translateX(-1px); }
  60% { transform: translateX(1px); }
  80% { transform: translateX(2px); }
  100% { transform: translateX(0); }
}

.glitch {
  position: relative;
  animation: glitch 0.3s infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  color: var(--neon-blue);
  z-index: -1;
  animation: glitch-1 0.5s infinite;
}

.glitch::after {
  color: var(--cyan-glow);
  z-index: -2;
  animation: glitch-2 0.7s infinite;
}

@keyframes glitch-1 {
  0% { transform: translateX(0); }
  25% { transform: translateX(-2px) translateY(1px); }
  50% { transform: translateX(1px) translateY(-1px); }
  75% { transform: translateX(-1px) translateY(1px); }
  100% { transform: translateX(0); }
}

@keyframes glitch-2 {
  0% { transform: translateX(0); }
  33% { transform: translateX(2px) translateY(-1px); }
  66% { transform: translateX(-1px) translateY(1px); }
  100% { transform: translateX(0); }
}

/* Typewriter effect */
@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--neon-blue); }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--neon-blue);
  white-space: nowrap;
  margin: 0 auto;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

/* Glow effects */
.blue-glow {
  box-shadow: 0 0 20px rgba(0, 191, 255, 0.5);
}

.cyan-glow {
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.red-glow {
  box-shadow: 0 0 10px rgba(255, 0, 60, 0.4);
}

/* Button styles */
.system-button {
  background: transparent;
  border: 1px solid var(--neon-blue);
  color: var(--neon-blue);
  padding: 12px 24px;
  font-family: 'Space Mono', monospace;
  font-size: 14px;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.system-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.2), transparent);
  transition: left 0.5s;
}

.system-button:hover::before {
  left: 100%;
}

.system-button:hover {
  color: #ffffff;
  border-color: #00FFFF;
  box-shadow: 0 0 20px rgba(0, 191, 255, 0.5);
  transform: scale(1.02);
}

/* Input styles */
.system-input {
  background: transparent;
  border: 1px solid var(--electric-blue);
  color: var(--text-white);
  padding: 12px 16px;
  font-family: 'Space Mono', monospace;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
  width: 100%;
}

.system-input:focus {
  border-color: var(--neon-blue);
  box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
}

.system-input::placeholder {
  color: #666666;
}

/* Background animation */
@keyframes digital-embers {
  0% { transform: translateY(100vh) translateX(-50px) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(50px) rotate(360deg); opacity: 0; }
}

.digital-ember {
  position: absolute;
  width: 2px;
  height: 10px;
  background: linear-gradient(to bottom, var(--neon-blue), transparent);
  animation: digital-embers linear infinite;
}

/* Fade in animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes characterFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 1s ease-out forwards;
}

.character-fade {
  opacity: 0;
  animation: characterFadeIn 0.1s ease-out forwards;
}

/* Section spacing */
.section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  position: relative;
}

.section-content {
  max-width: 1200px;
  width: 100%;
  text-align: center;
}

/* Progress bar */
.progress-bar {
  height: 2px;
  background: var(--neon-blue);
  transition: width 0.3s ease;
}

/* Audio toggle */
.audio-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: transparent;
  border: 1px solid var(--electric-blue);
  color: var(--electric-blue);
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.audio-toggle:hover {
  border-color: var(--neon-blue);
  color: var(--neon-blue);
}