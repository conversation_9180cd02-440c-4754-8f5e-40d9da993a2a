import React from 'react';

const PlayerLogs = () => {
  const logs = [
    {
      id: "#004",
      text: "Day 7. Failed my morning Penalty Quest. The HP drain is real. I've never been more focused in my life."
    },
    {
      id: "#011", 
      text: "Just hit Level 5. The System's message about the 'grace period ending'... it wasn't a joke. This just got serious."
    },
    {
      id: "#007",
      text: "My Discipline stat went up. The app didn't congratulate me. It just gave me a harder quest. That's how I knew it was working."
    }
  ];

  return (
    <section className="section" id="player-logs">
      <div className="section-content">
        <div className="max-w-4xl mx-auto">
          <h2 
            className="headline-font text-4xl md:text-5xl font-bold mb-16 text-center"
            style={{ color: '#ffffff' }}
          >
            Archived Transmissions
          </h2>
          
          <div className="space-y-8">
            {logs.map((log, index) => (
              <div 
                key={index}
                className="log-entry border-l-2 pl-6 py-4 relative"
                style={{ 
                  borderLeftColor: '#7E00F8',
                  background: 'linear-gradient(90deg, rgba(126, 0, 248, 0.1), transparent)',
                }}
              >
                {/* Glitch overlay effect */}
                <div 
                  className="absolute top-0 left-0 w-full h-full pointer-events-none opacity-20"
                  style={{
                    background: `repeating-linear-gradient(
                      90deg,
                      transparent,
                      transparent 2px,
                      rgba(126, 0, 248, 0.1) 2px,
                      rgba(126, 0, 248, 0.1) 4px
                    )`
                  }}
                ></div>
                
                <div className="flex items-start space-x-4">
                  <div 
                    className="terminal-font text-sm font-bold shrink-0"
                    style={{ color: '#00A8FF' }}
                  >
                    Player {log.id}
                  </div>
                  
                  <blockquote 
                    className="terminal-font text-sm leading-relaxed italic relative"
                    style={{ color: '#cccccc' }}
                  >
                    "{log.text}"
                    
                    {/* Data corruption effect */}
                    <span 
                      className="absolute -top-1 -left-2 w-full h-full pointer-events-none opacity-30"
                      style={{
                        background: 'repeating-linear-gradient(45deg, transparent, transparent 1px, rgba(255, 0, 60, 0.2) 1px, rgba(255, 0, 60, 0.2) 2px)',
                        animation: 'glitch 2s infinite'
                      }}
                    ></span>
                  </blockquote>
                </div>
                
                {/* Status indicator */}
                <div 
                  className="absolute left-0 top-1/2 w-3 h-3 rounded-full transform -translate-x-1/2 -translate-y-1/2"
                  style={{ 
                    background: '#7E00F8',
                    boxShadow: '0 0 10px rgba(126, 0, 248, 0.6)',
                    animation: 'pulse 2s infinite'
                  }}
                ></div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p 
              className="terminal-font text-xs"
              style={{ 
                color: '#666666',
                letterSpacing: '2px',
                textTransform: 'uppercase'
              }}
            >
              [DATA_FRAGMENTS_RECOVERED] // [AUTHENTICITY_VERIFIED]
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlayerLogs;