import axios from 'axios';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API functions for Solo Level Up System
export const systemAPI = {
  // Submit email for beta access
  submitBetaSignup: async (email) => {
    try {
      const response = await apiClient.post('/beta-signup', {
        email: email
      });
      
      return {
        success: true,
        message: response.data.message,
        data: response.data.data
      };
    } catch (error) {
      console.error('Beta signup error:', error);
      
      if (error.response?.data?.detail) {
        const errorDetail = error.response.data.detail;
        return {
          success: false,
          error: errorDetail.error || 'SYSTEM_ERROR',
          message: errorDetail.message || 'SYSTEM TEMPORARILY UNAVAILABLE'
        };
      }
      
      // Network or other errors
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'SYSTEM TEMPORARILY UNAVAILABLE'
      };
    }
  },

  // Get beta signup statistics
  getBetaStats: async () => {
    try {
      const response = await apiClient.get('/beta-signup/stats');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Beta stats error:', error);
      return {
        success: false,
        error: 'STATS_UNAVAILABLE',
        message: 'Unable to retrieve statistics'
      };
    }
  },

  // Health check
  healthCheck: async () => {
    try {
      const response = await apiClient.get('/');
      return {
        success: true,
        message: response.data.message
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        success: false,
        error: 'SYSTEM_OFFLINE'
      };
    }
  }
};

export default systemAPI;