// Mock data for Solo Level Up landing page
// This will be replaced with real backend integration later

// Mock player logs data
export const mockPlayerLogs = [
  {
    id: "#004",
    playerId: "004",
    timestamp: "2024-12-07T08:30:00Z",
    text: "Day 7. Failed my morning Penalty Quest. The HP drain is real. I've never been more focused in my life.",
    status: "active"
  },
  {
    id: "#011", 
    playerId: "011",
    timestamp: "2024-12-06T15:45:00Z",
    text: "Just hit Level 5. The System's message about the 'grace period ending'... it wasn't a joke. This just got serious.",
    status: "active"
  },
  {
    id: "#007",
    playerId: "007",
    timestamp: "2024-12-05T12:20:00Z",
    text: "My Discipline stat went up. The app didn't congratulate me. It just gave me a harder quest. That's how I knew it was working.",
    status: "active"
  }
];

// Mock core protocols data
export const mockCoreProtocols = [
  {
    id: "reality-quests",
    title: "REALITY QUESTS",
    description: "Your goals are now quests. Your effort is now XP. Failure has consequences.",
    iconType: "target",
    status: "active"
  },
  {
    id: "stat-evolution",
    title: "STAT EVOLUTION", 
    description: "Forge your build. Increase real-world Strength, Intelligence, and Discipline. Your stats are a reflection of your will.",
    iconType: "trending-up",
    status: "active"
  },
  {
    id: "system-narrator",
    title: "THE SYSTEM NARRATOR",
    description: "An omnipresent AI tracks your progress, issues commands, and delivers judgment. You are never alone.",
    iconType: "eye",
    status: "active"
  },
  {
    id: "level-threshold",
    title: "THE LEVEL 5 THRESHOLD",
    description: "The tutorial is free. Your freedom is not. After Level 5, the System will demand a tribute.",
    iconType: "shield",
    status: "active"
  }
];

// Mock API functions that simulate backend calls
export const mockAPI = {
  // Submit email for beta access
  submitBetaSignup: async (email) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (!email || !email.includes('@')) {
          reject(new Error('Invalid email address'));
        } else {
          // Store in localStorage for now (mock database)
          const existingSignups = JSON.parse(localStorage.getItem('betaSignups') || '[]');
          const newSignup = {
            id: Date.now().toString(),
            email: email,
            timestamp: new Date().toISOString(),
            status: 'pending'
          };
          existingSignups.push(newSignup);
          localStorage.setItem('betaSignups', JSON.stringify(existingSignups));
          
          resolve({
            success: true,
            message: 'DESIGNATION LOGGED. AWAIT THE CALL.',
            signupId: newSignup.id
          });
        }
      }, 1500); // Simulate network delay
    });
  },

  // Get beta signup count (for future use)
  getBetaSignupCount: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const signups = JSON.parse(localStorage.getItem('betaSignups') || '[]');
        resolve({
          count: signups.length,
          totalSlots: 1000,
          waitlistPosition: signups.length + 1
        });
      }, 500);
    });
  },

  // Check if email already exists
  checkEmailExists: async (email) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const signups = JSON.parse(localStorage.getItem('betaSignups') || '[]');
        const exists = signups.some(signup => signup.email === email);
        resolve({ exists });
      }, 300);
    });
  }
};

// Mock terminal messages for system boot sequence
export const mockTerminalMessages = [
  "> SYSTEM BOOT SEQUENCE COMPLETE.",
  "> SEARCHING FOR CANDIDATE... POTENTIAL DETECTED.", 
  "> AWAKENING PROTOCOL INITIATED. YOUR REALITY IS NOW A BETA TEST.",
  "> WELCOME, PLAYER."
];

// Mock sound effects (placeholder URLs - will be replaced with actual audio)
export const mockSoundEffects = {
  ambientLoop: '/assets/sounds/ambient-loop.mp3', // Low-frequency synth hum
  buttonClick: '/assets/sounds/button-click.mp3', // Sharp digital click
  successChime: '/assets/sounds/success-chime.mp3', // Soft system chime
  glitchEffect: '/assets/sounds/glitch.mp3', // Digital glitch sound
  typingSound: '/assets/sounds/typing.mp3' // Terminal typing sound
};