// Audio utility for managing game sounds
class AudioManager {
  constructor() {
    this.sounds = {};
    this.isEnabled = false; // Disabled by default
    this.volume = 0.5;
    this.initialized = false;
  }

  // Initialize audio files
  loadSounds() {
    if (this.initialized) return;

    const soundFiles = {
      hover: '/assets/audio/hover.mp3', // Replace with your actual filename
      opening: '/assets/audio/opening.mp3', // Replace with your actual filename
      typing: '/assets/audio/typing.mp3', // Replace with your actual filename
    };

    Object.keys(soundFiles).forEach(key => {
      try {
        this.sounds[key] = new Audio(soundFiles[key]);
        this.sounds[key].volume = this.volume;
        this.sounds[key].preload = 'auto';

        // Handle loading errors gracefully
        this.sounds[key].addEventListener('error', (e) => {
          console.warn(`Failed to load audio file: ${soundFiles[key]}`);
        });
      } catch (error) {
        console.warn(`Error creating audio for ${key}:`, error);
      }
    });

    this.initialized = true;
  }

  // Enable/disable audio
  setEnabled(enabled) {
    this.isEnabled = enabled;

    // Initialize sounds when first enabled
    if (enabled && !this.initialized) {
      this.loadSounds();
    }

    if (!enabled) {
      this.stopAll();
    }
  }

  // Get current enabled state
  getEnabled() {
    return this.isEnabled;
  }

  // Set volume for all sounds
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    Object.values(this.sounds).forEach(sound => {
      sound.volume = this.volume;
    });
  }

  // Play a sound
  play(soundName, options = {}) {
    if (!this.isEnabled || !this.sounds[soundName]) return null;

    const sound = this.sounds[soundName];

    try {
      // Reset to beginning if already playing
      sound.currentTime = 0;

      // Apply options
      if (options.loop !== undefined) {
        sound.loop = options.loop;
      }
      if (options.volume !== undefined) {
        sound.volume = Math.max(0, Math.min(1, options.volume));
      }

      // Play the sound
      const playPromise = sound.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn(`Failed to play sound ${soundName}:`, error);
        });
      }

      return sound;
    } catch (error) {
      console.warn(`Error playing sound ${soundName}:`, error);
      return null;
    }
  }

  // Stop a specific sound
  stop(soundName) {
    if (this.sounds[soundName]) {
      try {
        this.sounds[soundName].pause();
        this.sounds[soundName].currentTime = 0;
      } catch (error) {
        console.warn(`Error stopping sound ${soundName}:`, error);
      }
    }
  }

  // Stop all sounds
  stopAll() {
    Object.values(this.sounds).forEach(sound => {
      try {
        sound.pause();
        sound.currentTime = 0;
      } catch (error) {
        console.warn('Error stopping sound:', error);
      }
    });
  }

  // Play hover sound
  playHover() {
    this.play('hover', { volume: 0.3 });
  }

  // Play opening sound (looped during typing animation)
  playOpening() {
    return this.play('opening', { loop: true, volume: 0.4 });
  }

  // Stop opening sound
  stopOpening() {
    this.stop('opening');
  }

  // Play typing sound
  playTyping() {
    this.play('typing', { volume: 0.2 });
  }
}

// Create singleton instance
const audioManager = new AudioManager();

export default audioManager;
