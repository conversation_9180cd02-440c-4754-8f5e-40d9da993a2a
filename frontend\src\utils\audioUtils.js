// Audio utility for managing game sounds
class AudioManager {
  constructor() {
    this.sounds = {};
    this.isEnabled = false;
    this.volume = 0.5;
  }

  // Initialize audio files
  loadSounds() {
    const soundFiles = {
      hover: '/assets/audio/hover.mp3', // Replace with your actual filename
      opening: '/assets/audio/opening.mp3', // Replace with your actual filename
      typing: '/assets/audio/typing.mp3', // Replace with your actual filename
    };

    Object.keys(soundFiles).forEach(key => {
      this.sounds[key] = new Audio(soundFiles[key]);
      this.sounds[key].volume = this.volume;
      this.sounds[key].preload = 'auto';
    });
  }

  // Enable/disable audio
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.stopAll();
    }
  }

  // Set volume for all sounds
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    Object.values(this.sounds).forEach(sound => {
      sound.volume = this.volume;
    });
  }

  // Play a sound
  play(soundName, options = {}) {
    if (!this.isEnabled || !this.sounds[soundName]) return;

    const sound = this.sounds[soundName];
    
    // Reset to beginning if already playing
    sound.currentTime = 0;
    
    // Apply options
    if (options.loop !== undefined) {
      sound.loop = options.loop;
    }
    if (options.volume !== undefined) {
      sound.volume = Math.max(0, Math.min(1, options.volume));
    }

    // Play the sound
    sound.play().catch(error => {
      console.warn(`Failed to play sound ${soundName}:`, error);
    });

    return sound;
  }

  // Stop a specific sound
  stop(soundName) {
    if (this.sounds[soundName]) {
      this.sounds[soundName].pause();
      this.sounds[soundName].currentTime = 0;
    }
  }

  // Stop all sounds
  stopAll() {
    Object.values(this.sounds).forEach(sound => {
      sound.pause();
      sound.currentTime = 0;
    });
  }

  // Play hover sound
  playHover() {
    this.play('hover', { volume: 0.3 });
  }

  // Play opening sound (looped during typing animation)
  playOpening() {
    return this.play('opening', { loop: true, volume: 0.4 });
  }

  // Stop opening sound
  stopOpening() {
    this.stop('opening');
  }

  // Play typing sound
  playTyping() {
    this.play('typing', { volume: 0.2 });
  }
}

// Create singleton instance
const audioManager = new AudioManager();

export default audioManager;
