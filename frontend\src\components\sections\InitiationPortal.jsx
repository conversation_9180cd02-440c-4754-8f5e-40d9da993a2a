import React, { useState } from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import { systemAPI } from '../../utils/api';

const InitiationPortal = () => {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.3 });
  const [email, setEmail] = useState('');
  const [progress, setProgress] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmail(value);
    setError(''); // Clear any previous errors
    
    // Update progress bar based on email validity
    if (value.length === 0) {
      setProgress(0);
    } else if (value.includes('@') && value.includes('.')) {
      setProgress(100);
    } else if (value.includes('@')) {
      setProgress(70);
    } else {
      setProgress(40);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      setError('INVALID DESIGNATION FORMAT');
      return;
    }

    setIsSubmitting(true);
    setError('');
    
    try {
      const result = await systemAPI.submitBetaSignup(email);
      
      if (result.success) {
        setIsSubmitted(true);
      } else {
        setError(result.message || 'DESIGNATION PROCESSING FAILED');
      }
    } catch (err) {
      console.error('Submission error:', err);
      setError('SYSTEM TEMPORARILY UNAVAILABLE');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <section className="section" id="initiation-portal" ref={sectionRef}>
        <div className="section-content">
          <div className="max-w-2xl mx-auto text-center px-4">
            <div className="fade-in">
              <div 
                className="text-xl sm:text-2xl md:text-3xl mb-8 terminal-font"
                style={{ 
                  color: '#00BFFF', 
                  letterSpacing: '2px',
                  lineHeight: '1.4'
                }}
              >
                [DESIGNATION LOGGED. AWAIT THE CALL.]
              </div>
              
              <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 mx-auto mb-8">
                <div 
                  className="w-full h-full rounded-full border-2 animate-spin"
                  style={{ 
                    borderColor: '#00BFFF transparent #00BFFF transparent',
                    animation: 'spin 2s linear infinite'
                  }}
                ></div>
              </div>

              <p 
                className="terminal-font text-sm"
                style={{ color: '#666666' }}
              >
                The System will contact you when a slot becomes available.
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="section" id="initiation-portal" ref={sectionRef}>
      <div className="section-content">
        <div className="max-w-2xl mx-auto text-center px-4">
          <h2 
            className="headline-font text-3xl sm:text-4xl md:text-5xl font-bold mb-6 sm:mb-8"
            style={{ 
              color: '#ffffff',
              lineHeight: '1.2',
              letterSpacing: '-0.02em'
            }}
          >
            Your Awakening Begins Soon.
          </h2>
          
          <p 
            className="terminal-font text-base sm:text-lg mb-8 sm:mb-12"
            style={{ 
              color: '#cccccc',
              lineHeight: '1.4'
            }}
          >
            Leave your identifier. The System will contact you when a slot becomes available.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <input 
                type="email"
                value={email}
                onChange={handleEmailChange}
                placeholder="Enter your designation..."
                className="system-input text-base sm:text-lg py-3 sm:py-4 px-4 sm:px-6 w-full"
                disabled={isSubmitting}
                required
              />
              
              {/* Progress bar */}
              <div 
                className="absolute bottom-0 left-0 h-0.5 transition-all duration-300"
                style={{
                  width: `${progress}%`,
                  background: '#00BFFF',
                  boxShadow: progress > 0 ? '0 0 10px rgba(0, 191, 255, 0.5)' : 'none'
                }}
              ></div>
            </div>

            {/* Error display */}
            {error && (
              <div 
                className="terminal-font text-sm text-center"
                style={{ 
                  color: '#FF003C',
                  textShadow: '0 0 10px rgba(255, 0, 60, 0.5)',
                  letterSpacing: '1px'
                }}
              >
                [ERROR] {error}
              </div>
            )}

            <button 
              type="submit"
              className="system-button text-base sm:text-lg px-8 sm:px-12 py-3 sm:py-4"
              disabled={isSubmitting || !email.includes('@')}
              style={{
                opacity: (isSubmitting || !email.includes('@')) ? 0.5 : 1,
                cursor: (isSubmitting || !email.includes('@')) ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                transform: 'translateZ(0)'
              }}
            >
              {isSubmitting ? '[PROCESSING...]' : '[REQUEST ACCESS]'}
            </button>
          </form>

          <footer className="mt-12 sm:mt-16">
            <p 
              className="terminal-font text-xs leading-relaxed px-4"
              style={{ 
                color: '#444444',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.5'
              }}
            >
              Solo Level Up is an independent project in a closed alpha state. The System is unstable. 
              By signing up, you acknowledge the risks and request access to the Awakening Protocol.
            </p>
          </footer>
        </div>
      </div>
    </section>
  );
};

export default InitiationPortal;