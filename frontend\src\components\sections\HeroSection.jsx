import React, { useState, useEffect } from 'react';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import audioManager from '../../utils/audioUtils';

const HeroSection = ({ onScrollToInitiation, audioEnabled }) => {
  const [sectionRef, isVisible] = useIntersectionObserver({ threshold: 0.1 });
  const [titleVisible, setTitleVisible] = useState(false);
  const [subtitleVisible, setSubtitleVisible] = useState(false);
  const [ctaVisible, setCtaVisible] = useState(false);

  const title1 = "You are not motivated.";
  const title2 = "You are commanded.";
  const subtitle = "Solo Level Up. The System is waiting.";

  useEffect(() => {
    if (isVisible) {
      const timer1 = setTimeout(() => setTitleVisible(true), 500);
      const timer2 = setTimeout(() => setSubtitleVisible(true), 3000);
      const timer3 = setTimeout(() => setCtaVisible(true), 4500);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [isVisible]);

  const renderCharacters = (text, visible) => {
    if (!visible) return null;
    
    return text.split('').map((char, index) => (
      <span
        key={index}
        className="character-fade"
        style={{
          animationDelay: `${index * 0.05}s`,
          display: char === ' ' ? 'inline' : 'inline-block'
        }}
      >
        {char === ' ' ? '\u00A0' : char}
      </span>
    ));
  };

  return (
    <section className="section" id="hero" ref={sectionRef}>
      <div className="section-content">
        <div className="hero-content">
          <h1 
            className="headline-font text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-black mb-8 leading-tight text-center"
            style={{ 
              color: '#ffffff',
              lineHeight: '1.1',
              letterSpacing: '-0.02em'
            }}
          >
            <div className="mb-4">
              {renderCharacters(title1, titleVisible)}
            </div>
            <div>
              {renderCharacters(title2, titleVisible)}
            </div>
          </h1>

          {subtitleVisible && (
            <h2 
              className="headline-font text-lg sm:text-2xl md:text-3xl mb-16 fade-in text-center"
              style={{ color: '#00BFFF' }}
            >
              {subtitle}
            </h2>
          )}

          {ctaVisible && (
            <div className="fade-in text-center">
              <button
                className="system-button text-lg px-8 py-4"
                onClick={onScrollToInitiation}
                onMouseEnter={() => {
                  console.log('🎮 Hover: Become Player button');
                  audioManager.playHover();
                }}
                style={{ transform: 'translateZ(0)' }}
              >
                [BECOME A PLAYER]
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;