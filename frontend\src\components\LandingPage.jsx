import React, { useState, useEffect } from 'react';
import { Volume2, VolumeX } from 'lucide-react';
import HeroSection from './sections/HeroSection';
import SystemTerminal from './sections/SystemTerminal';
import CoreProtocols from './sections/CoreProtocols';
import Differentiation from './sections/Differentiation';
import PlayerLogs from './sections/PlayerLogs';
import InitiationPortal from './sections/InitiationPortal';
import BackgroundAnimation from './BackgroundAnimation';

const LandingPage = () => {
  const [audioEnabled, setAudioEnabled] = useState(false);

  const toggleAudio = () => {
    setAudioEnabled(!audioEnabled);
    // TODO: Implement audio controls when backend is ready
  };

  const scrollToInitiation = () => {
    const initiationSection = document.getElementById('initiation-portal');
    if (initiationSection) {
      initiationSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="landing-page">
      <BackgroundAnimation />
      
      {/* Audio Toggle */}
      <button 
        className="audio-toggle"
        onClick={toggleAudio}
        title={audioEnabled ? "SENSORY_LINK: DISENGAGE" : "SENSORY_LINK: ENGAGE"}
      >
        {audioEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
      </button>

      {/* Sections */}
      <HeroSection onScrollToInitiation={scrollToInitiation} />
      <SystemTerminal />
      <CoreProtocols />
      <Differentiation />
      <PlayerLogs />
      <InitiationPortal />
    </div>
  );
};

export default LandingPage;