import React, { useState, useEffect } from 'react';
import { Volume2, VolumeX } from 'lucide-react';
import HeroSection from './sections/HeroSection';
import SystemTerminal from './sections/SystemTerminal';
import CoreProtocols from './sections/CoreProtocols';
import Differentiation from './sections/Differentiation';
import PlayerLogs from './sections/PlayerLogs';
import InitiationPortal from './sections/InitiationPortal';
import BackgroundAnimation from './BackgroundAnimation';
import audioManager from '../utils/audioUtils';

const LandingPage = () => {
  const [audioEnabled, setAudioEnabled] = useState(false); // Disabled by default

  const toggleAudio = () => {
    const newAudioState = !audioEnabled;
    setAudioEnabled(newAudioState);
    audioManager.setEnabled(newAudioState);

    // Provide user feedback
    console.log(`🔊 Audio ${newAudioState ? 'ENABLED' : 'DISABLED'}`);
    console.log(`🎵 Audio Manager State:`, {
      enabled: audioManager.getEnabled(),
      initialized: audioManager.initialized
    });
  };

  // Initialize audio manager on component mount
  useEffect(() => {
    // Ensure audio is disabled by default
    audioManager.setEnabled(false);
  }, []);

  const scrollToInitiation = () => {
    const initiationSection = document.getElementById('initiation-portal');
    if (initiationSection) {
      initiationSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="landing-page">
      <BackgroundAnimation />
      
      {/* Audio Toggle */}
      <button
        className={`audio-toggle ${audioEnabled ? 'enabled' : 'disabled'}`}
        onClick={toggleAudio}
        title={audioEnabled ? "SENSORY_LINK: DISENGAGE" : "SENSORY_LINK: ENGAGE"}
      >
        {audioEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
      </button>

      {/* Sections */}
      <HeroSection onScrollToInitiation={scrollToInitiation} audioEnabled={audioEnabled} />
      <SystemTerminal audioEnabled={audioEnabled} />
      <CoreProtocols audioEnabled={audioEnabled} />
      <Differentiation />
      <PlayerLogs />
      <InitiationPortal />
    </div>
  );
};

export default LandingPage;